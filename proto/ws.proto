syntax = "proto3";

// ============================================================================
// 客户端发送给服务器的消息类型
// ============================================================================

// 客户端信息
message ClientInfoMessage {
    string type = 1;           // "client_info"
    string context = 2;        // 客户端上下文信息
    int64 timestamp = 3;       // 时间戳
}

// 心跳消息
message HeartbeatMessage {
    string type = 1;           // "heartbeat"
    string context = 2;        // 客户端上下文
    int32 totalConnections = 3; // 总连接数
    int64 timestamp = 4;       // 时间戳
}

// 直播间连接响应
message LiveRoomResponseMessage {
    string type = 1;           // "live_room_response"
    string requestId = 2;      // 请求ID
    bool success = 3;          // 是否成功
    string connectionId = 4;   // 连接ID（成功时）
    string liveUrl = 5;        // 直播间URL
    string error = 6;          // 错误信息（失败时）
    int64 timestamp = 7;       // 时间戳
}

// 连接信息
message ConnectionInfo {
    string id = 1;             // 连接ID
    string liveUrl = 2;        // 直播间URL
    string status = 3;         // 状态：active/inactive
    bool windowVisible = 4;    // 窗口是否可见
}

// 全局统计信息
message GlobalStats {
    int64 totalLikes = 1;      // 总点赞数
    int64 totalWatching = 2;   // 总观看数
    int64 totalComments = 3;   // 总评论数
    int64 totalGifts = 4;      // 总礼物数
}

// 状态信息
message StatusInfo {
    string context = 1;        // 客户端上下文
    bool isConnected = 2;      // 是否连接
    int32 totalConnections = 3; // 总连接数
    repeated ConnectionInfo connections = 4; // 连接列表
    GlobalStats globalStats = 5; // 全局统计
    int64 timestamp = 6;       // 时间戳
}

// 状态响应
message StatusResponseMessage {
    string type = 1;           // "status_response"
    string requestId = 2;      // 请求ID
    StatusInfo status = 3;     // 状态信息
    int64 timestamp = 4;       // 时间戳
}

// 直播事件数据（这里使用通用的JSON字符串，因为事件数据结构复杂）
message LiveEventsMessage {
    string type = 1;           // "live_events"
    string connectionId = 2;   // 连接ID
    string liveUrl = 3;        // 直播间URL
    string data = 4;           // 事件数据（JSON字符串）
    int64 timestamp = 5;       // 时间戳
    string source = 6;         // 数据源："LiveTiktok"
    string context = 7;        // 客户端上下文
}

// 错误消息
message ErrorMessage {
    string type = 1;           // "error"
    string connectionId = 2;   // 连接ID（可选）
    string liveUrl = 3;        // 直播间URL（可选）
    string error = 4;          // 错误信息
    int64 timestamp = 5;       // 时间戳
    string context = 6;        // 客户端上下文
}

// 客户端关闭通知
message ClientShutdownMessage {
    string type = 1;           // "client_shutdown"
    string message = 2;        // 关闭消息
    int32 totalConnections = 3; // 总连接数
    int64 timestamp = 4;       // 时间戳
    string context = 5;        // 客户端上下文
}

// 自定义消息
message CustomMessage {
    string type = 1;           // "custom"
    string data = 2;           // 自定义数据（JSON字符串）
    int64 timestamp = 3;       // 时间戳
    string source = 4;         // 数据源："bridge"
    string context = 5;        // 客户端上下文
}

// ============================================================================
// 服务器发送给客户端的消息类型
// ============================================================================

// 欢迎消息
message WelcomeMessage {
    string type = 1;           // "welcome"
    string message = 2;        // 欢迎信息
    string clientId = 3;       // 客户端ID
    int64 timestamp = 4;       // 时间戳
}

// 直播间连接请求
message LiveRoomRequestMessage {
    string type = 1;           // "live_room_request"
    string liveUrl = 2;        // 直播间URL
    string requestId = 3;      // 请求ID
    string options = 4;        // 连接选项（JSON字符串）
    int64 timestamp = 5;       // 时间戳
}

// 断开直播间请求
message DisconnectRoomRequestMessage {
    string type = 1;           // "disconnect_room_request"
    string connectionId = 2;   // 连接ID
    string requestId = 3;      // 请求ID
    int64 timestamp = 4;       // 时间戳
}

// 获取状态请求
message GetStatusRequestMessage {
    string type = 1;           // "get_status_request"
    string requestId = 2;      // 请求ID
    int64 timestamp = 3;       // 时间戳
}

// 服务器广播消息
message ServerBroadcastMessage {
    string type = 1;           // "server_broadcast"
    string message = 2;        // 广播消息
    int64 timestamp = 3;       // 时间戳
}

// 服务器错误消息
message ServerErrorMessage {
    string type = 1;           // "error"
    string message = 2;        // 错误消息
    int64 timestamp = 3;       // 时间戳
}

// ============================================================================
// 通用消息包装器
// ============================================================================

// 客户端到服务器的消息包装器
message ClientToServerMessage {
    oneof message {
        ClientInfoMessage clientInfo = 1;
        HeartbeatMessage heartbeat = 2;
        LiveRoomResponseMessage liveRoomResponse = 3;
        StatusResponseMessage statusResponse = 4;
        LiveEventsMessage liveEvents = 5;
        ErrorMessage error = 6;
        ClientShutdownMessage clientShutdown = 7;
        CustomMessage custom = 8;
    }
}

// 服务器到客户端的消息包装器
message ServerToClientMessage {
    oneof message {
        WelcomeMessage welcome = 1;
        LiveRoomRequestMessage liveRoomRequest = 2;
        DisconnectRoomRequestMessage disconnectRoomRequest = 3;
        GetStatusRequestMessage getStatusRequest = 4;
        ServerBroadcastMessage serverBroadcast = 5;
        ServerErrorMessage serverError = 6;
    }
}
